import { Download, GraduationCap, Briefcase, Award, Languages, Loader2 } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useState } from 'react';

const About = () => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const generatePDF = async () => {
    setIsGeneratingPDF(true);
    try {
      // إنشاء مستند PDF جديد
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      // إعدادات الألوان
      const primaryColor = [245, 158, 11]; // اللون الذهبي
      const textColor = [0, 0, 0]; // اللون الأسود
      const grayColor = [100, 100, 100]; // اللون الرمادي

      // إعدادات الخط
      doc.setFont("helvetica");

      // دالة مساعدة لإضافة نص عربي
      const addArabicText = (text: string, x: number, y: number, options: any = {}) => {
        const fontSize = options.fontSize || 10;
        const color = options.color || textColor;
        const align = options.align || 'right';

        doc.setFontSize(fontSize);
        doc.setTextColor(color[0], color[1], color[2]);
        doc.text(text, x, y, { align, maxWidth: options.maxWidth || 170 });
      };

      // دالة مساعدة لإضافة عنوان قسم
      const addSectionTitle = (title: string, y: number) => {
        doc.setFontSize(14);
        doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        doc.text(title, 190, y, { align: 'right' });

        // خط تحت العنوان
        doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        doc.setLineWidth(0.5);
        doc.line(20, y + 2, 190, y + 2);

        return y + 8;
      };

      let currentY = 20;

      // رأس الصفحة - الاسم والمسمى الوظيفي
      addArabicText('حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي', 105, currentY, {
        fontSize: 18,
        color: primaryColor,
        align: 'center'
      });
      currentY += 8;

      addArabicText('مهندس تقنية معلومات', 105, currentY, {
        fontSize: 12,
        color: grayColor,
        align: 'center'
      });
      currentY += 15;

      // خط فاصل
      doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setLineWidth(0.5);
      doc.line(20, currentY, 190, currentY);
      currentY += 10;

      // قسم معلومات التواصل
      currentY = addSectionTitle('معلومات التواصل', currentY);
      addArabicText('<EMAIL> :البريد الإلكتروني', 190, currentY, { fontSize: 10 });
      currentY += 6;
      addArabicText('+967 777548421 / +967 718706242 :الهاتف', 190, currentY, { fontSize: 10 });
      currentY += 6;
      addArabicText('عدن، اليمن :الموقع', 190, currentY, { fontSize: 10 });
      currentY += 15;

      // قسم الملخص المهني
      currentY = addSectionTitle('الملخص المهني', currentY);
      const summaryText = `طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: TypeScript, JavaScript, HTML5, CSS3, C++, C#, PHP, SQL كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على SQL Server و MySQL وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.`;

      const splitSummary = doc.splitTextToSize(summaryText, 170);
      doc.setFontSize(9);
      doc.setTextColor(textColor[0], textColor[1], textColor[2]);
      doc.text(splitSummary, 190, currentY, { align: 'right' });
      currentY += splitSummary.length * 4 + 10;
      
      // قسم التعليم
      currentY = addSectionTitle('التعليم', currentY);
      addArabicText('جامعة عدن - كلية الهندسة •', 190, currentY, { fontSize: 10 });
      currentY += 6;
      addArabicText('بكالوريوس تقنية المعلومات - السنة الرابعة •', 190, currentY, { fontSize: 10 });
      currentY += 15;

      // قسم الخبرة العملية
      currentY = addSectionTitle('الخبرة العملية', currentY);
      const experience1 = 'عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية •';
      const splitExp1 = doc.splitTextToSize(experience1, 170);
      doc.setFontSize(9);
      doc.setTextColor(textColor[0], textColor[1], textColor[2]);
      doc.text(splitExp1, 190, currentY, { align: 'right' });
      currentY += splitExp1.length * 4 + 3;

      addArabicText('أعمل في مجال التجارة الحرة •', 190, currentY, { fontSize: 9 });
      currentY += 15;

      // قسم المشاريع
      currentY = addSectionTitle('المشاريع', currentY);

      const projects = [
        'برنامج إدارة الحجز - برنامج desktop لإدارة عمليات الحجز •',
        'متجر إلكتروني بواسطة Laravel 11 للأطفال حديثي الولادة •',
        'تصميم قواعد البيانات للمدارس والفنادق •',
        'الموقع الشخصي - تم بناؤه باستخدام React 18, TypeScript, Tailwind CSS •'
      ];

      projects.forEach(project => {
        const splitProject = doc.splitTextToSize(project, 170);
        doc.setFontSize(9);
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.text(splitProject, 190, currentY, { align: 'right' });
        currentY += splitProject.length * 4 + 3;
      });
      currentY += 10;
      
      // التحقق من الحاجة لصفحة جديدة
      if (currentY > 250) {
        doc.addPage();
        currentY = 20;
      }

      // قسم المهارات التقنية
      currentY = addSectionTitle('المهارات التقنية', currentY);

      const skills = [
        'لغات البرمجة: TypeScript, JavaScript, HTML5, CSS3, C++, C#, PHP, SQL •',
        'Frontend: React 18, React Router, React Query, Tailwind CSS, ShadCN/UI •',
        'أدوات التطوير: Vite, Node.js, ESLint, Git, GitHub Actions •',
        'Backend: Laravel Framework, RESTful APIs •',
        'قواعد البيانات: MySQL, SQL Server •',
        'التصميم: Figma (UX/UI), Responsive Design, Dark/Light Mode •',
        'Microsoft Office Suite •'
      ];

      skills.forEach(skill => {
        const splitSkill = doc.splitTextToSize(skill, 170);
        doc.setFontSize(9);
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.text(splitSkill, 190, currentY, { align: 'right' });
        currentY += splitSkill.length * 4 + 2;
      });
      currentY += 10;

      // قسم اللغات
      currentY = addSectionTitle('اللغات', currentY);
      addArabicText('العربية: الأم •', 190, currentY, { fontSize: 10 });
      currentY += 6;
      addArabicText('الإنجليزية: ممتاز •', 190, currentY, { fontSize: 10 });
      currentY += 6;
      addArabicText('الفرنسية: متوسط •', 190, currentY, { fontSize: 10 });
      currentY += 15;

      // التحقق من الحاجة لصفحة جديدة للدورات
      if (currentY > 220) {
        doc.addPage();
        currentY = 20;
      }

      // قسم الدورات
      currentY = addSectionTitle('الدورات التدريبية', currentY);

      const courses = [
        'أساسيات البرمجة و C++ - منصة Programming Advices (2022-2024) •',
        'تطوير تطبيقات سطح المكتب - منصة Programming Advices (2024) •',
        'تطوير المواقع Frontend - منصة Alzero Web School •',
        'تطوير المواقع Backend - أكاديمية الجيل العربي •',
        'اللغة الإنجليزية - معهد أميدست الأمريكي (سنة كاملة) •',
        'CCNA - سبتمبر 2023 •',
        'CPS - معهد أميديست الأمريكي •'
      ];

      courses.forEach(course => {
        const splitCourse = doc.splitTextToSize(course, 170);
        doc.setFontSize(9);
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.text(splitCourse, 190, currentY, { align: 'right' });
        currentY += splitCourse.length * 4 + 2;
      });
      currentY += 10;

      // قسم مشروع الموقع الشخصي
      if (currentY > 240) {
        doc.addPage();
        currentY = 20;
      }

      currentY = addSectionTitle('مشروع الموقع الشخصي', currentY);

      const websiteDetails = [
        'تم بناء الموقع باستخدام: React 18, TypeScript, Vite •',
        'التصميم: Tailwind CSS, ShadCN/UI, Responsive Design •',
        'إدارة البيانات: React Query, RESTful APIs •',
        'النشر: GitHub Pages, GitHub Actions (CI/CD) •'
      ];

      websiteDetails.forEach(detail => {
        const splitDetail = doc.splitTextToSize(detail, 170);
        doc.setFontSize(9);
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.text(splitDetail, 190, currentY, { align: 'right' });
        currentY += splitDetail.length * 4 + 2;
      });

      // إضافة تاريخ الإنشاء في أسفل الصفحة
      const currentDate = new Date().toLocaleDateString('ar-EG');
      doc.setFontSize(8);
      doc.setTextColor(grayColor[0], grayColor[1], grayColor[2]);
      doc.text(`تم إنشاء هذه السيرة الذاتية في: ${currentDate}`, 105, 285, { align: 'center' });

      // إنشاء اسم الملف مع التاريخ
      const fileName = `Hodifa_AlHodify_CV_${new Date().toISOString().split('T')[0]}.pdf`;

      // حفظ الملف
      doc.save(fileName);

      // إظهار رسالة نجاح
      alert('✅ تم تحميل السيرة الذاتية بنجاح!\n\nاسم الملف: ' + fileName);
      console.log('تم إنشاء ملف PDF بنجاح:', fileName);
    } catch (error) {
      console.error('خطأ في إنشاء ملف PDF:', error);

      // إظهار رسالة خطأ للمستخدم
      alert('حدث خطأ في إنشاء ملف PDF. سيتم تحميل ملف نصي بدلاً من ذلك.');

      // إنشاء ملف نصي احتياطي
      const cvContent = `
═══════════════════════════════════════════════════════════════
                    السيرة الذاتية
═══════════════════════════════════════════════════════════════

الاسم: حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
المسمى الوظيفي: مهندس تقنية معلومات

═══════════════════════════════════════════════════════════════
                   معلومات التواصل
═══════════════════════════════════════════════════════════════

البريد الإلكتروني: <EMAIL>
الهاتف: +967 777548421 / +967 718706242
الموقع: عدن، اليمن

═══════════════════════════════════════════════════════════════
                    الملخص المهني
═══════════════════════════════════════════════════════════════

طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: TypeScript, JavaScript, HTML5, CSS3, C++, C#, PHP, SQL كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على SQL Server و MySQL وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.

═══════════════════════════════════════════════════════════════
                      التعليم
═══════════════════════════════════════════════════════════════

• جامعة عدن - كلية الهندسة
• بكالوريوس تقنية المعلومات - السنة الرابعة

═══════════════════════════════════════════════════════════════
                   المهارات التقنية
═══════════════════════════════════════════════════════════════

• لغات البرمجة: TypeScript, JavaScript, HTML5, CSS3, C++, C#, PHP, SQL
• Frontend: React 18, React Router, React Query, Tailwind CSS, ShadCN/UI
• أدوات التطوير: Vite, Node.js, ESLint, Git, GitHub Actions
• Backend: Laravel Framework, RESTful APIs
• قواعد البيانات: MySQL, SQL Server
• التصميم: Figma (UX/UI), Responsive Design, Dark/Light Mode
• Microsoft Office Suite

═══════════════════════════════════════════════════════════════
                  مشروع الموقع الشخصي
═══════════════════════════════════════════════════════════════

• تم بناء الموقع باستخدام: React 18, TypeScript, Vite
• التصميم: Tailwind CSS, ShadCN/UI, Responsive Design
• إدارة البيانات: React Query, RESTful APIs
• النشر: GitHub Pages, GitHub Actions (CI/CD)

═══════════════════════════════════════════════════════════════
                      اللغات
═══════════════════════════════════════════════════════════════

• العربية: الأم
• الإنجليزية: ممتاز
• الفرنسية: متوسط

═══════════════════════════════════════════════════════════════
تم إنشاء هذه السيرة الذاتية في: ${new Date().toLocaleDateString('ar-EG')}
═══════════════════════════════════════════════════════════════
      `;

      try {
        const blob = new Blob([cvContent], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Hodifa_AlHodify_CV_${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log('تم إنشاء ملف نصي احتياطي بنجاح');
      } catch (fallbackError) {
        console.error('خطأ في إنشاء الملف الاحتياطي:', fallbackError);
        alert('عذراً، حدث خطأ في تحميل السيرة الذاتية. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">السيرة</span> الذاتية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <Button
              onClick={generatePDF}
              disabled={isGeneratingPDF}
              className="bg-amber-500 hover:bg-amber-600 disabled:bg-amber-400 disabled:cursor-not-allowed text-black font-semibold px-8 py-3 rounded-full transition-all duration-300"
            >
              {isGeneratingPDF ? (
                <>
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  جاري إنشاء الملف...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 ml-2" />
                  تحميل السيرة الذاتية PDF
                </>
              )}
            </Button>
          </div>

          {/* Personal Info with Profile Photo */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <div className="flex flex-col items-center space-y-6">
                {/* Profile Photo */}
                <div className="relative w-32 h-32 group">
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-500/30 to-amber-600/30 rounded-full animate-pulse"></div>
                  <div className="absolute inset-1 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full"></div>
                  <div className="absolute inset-2 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-500">
                    <img
                      src="/profile-photo.jpg"
                      alt="حذيفه عبدالمعز الحذيفي"
                      className="w-full h-full object-cover transition-all duration-500 group-hover:brightness-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-amber-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                </div>

                <div className="text-center">
                  <CardTitle className="text-2xl text-amber-400">
                    حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
                  </CardTitle>
                  <p className="text-xl text-gray-300 mt-2">مهندس تقنية معلومات</p>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الملخص المهني
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 leading-relaxed">
                طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: 
                C++, C#, Html, Css, Js, Php, Sql كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على Sqlserver و Mysql وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.
              </p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <GraduationCap className="w-5 h-5 ml-2" />
                  التعليم
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-200">جامعة عدن - كلية الهندسة</h3>
                    <p className="text-gray-400">طالب بكلاريوس – تقنية المعلومات</p>
                    <p className="text-gray-400">السنة الرابعة</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <Languages className="w-5 h-5 ml-2" />
                  اللغات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">العربية</span>
                    <span className="text-amber-400">الأم</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الإنجليزية</span>
                    <span className="text-amber-400">ممتاز</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الفرنسية</span>
                    <span className="text-amber-400">متوسط</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الخبرة العملية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">كما أنني أعمل في مجال التجارة الحرة.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المشاريع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">برنامج إدارة الحجز</h4>
                  <p className="text-gray-300">بناء برنامج desktop حر وبسيط في عمليات الحجز وفكرة البرنامج شاملة لأي جانب يتطلب إدارة الحجز بشكل عام.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">متجر إلكتروني - Laravel 11</h4>
                  <p className="text-gray-300">بناء موقع تجاري حر بإستخدام بيئة التعامل Laravel11 وقد خصصت الموقع أن يكون متجر يلبي احتياجات الأطفال حديثي الولادة حتى سن السنتين ويجري التطوير عليه إلى أن يكون الموقع أكثر مرونة لأي جانب تجاري محتمل.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">تصميم قواعد البيانات للمدارس</h4>
                  <p className="text-gray-300">عملت كشريك في تحليل وتصميم قواعد بيانات لأحدى مدارس القرى اليمنية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">نظام إدارة الفنادق</h4>
                  <p className="text-gray-300">تحليل وتصميم قاعدة بيانات لأحد الفنادق في عدن</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Award className="w-5 h-5 ml-2" />
                الدورات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">أساسيات البرمجة و C++</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2022-2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير تطبيقات سطح المكتب</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Frontend</h4>
                  <p className="text-gray-400">منصة Alzero Web School المصرية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Backend</h4>
                  <p className="text-gray-400">أكاديمية الجيل العربي - اليمن</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">اللغة الإنجليزية</h4>
                  <p className="text-gray-400">معهد أميدست الأمريكي - عدن (سنة كاملة)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CCNA</h4>
                  <p className="text-gray-400">سبتمبر 2023</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CPS</h4>
                  <p className="text-gray-400">معهد أميديست الأمريكي</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المهارات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">لغات البرمجة</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• TypeScript</li>
                    <li>• JavaScript (ES6+)</li>
                    <li>• HTML5</li>
                    <li>• CSS3</li>
                    <li>• C++</li>
                    <li>• C#</li>
                    <li>• PHP</li>
                    <li>• SQL</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">Frontend التقنيات</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• React 18</li>
                    <li>• React Router DOM</li>
                    <li>• React Query (TanStack)</li>
                    <li>• Tailwind CSS</li>
                    <li>• Radix UI</li>
                    <li>• ShadCN/UI</li>
                    <li>• Lucide React Icons</li>
                    <li>• Next Themes</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">أدوات التطوير</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• Vite</li>
                    <li>• Node.js</li>
                    <li>• npm/yarn</li>
                    <li>• ESLint</li>
                    <li>• PostCSS</li>
                    <li>• Autoprefixer</li>
                    <li>• Git & GitHub</li>
                    <li>• GitHub Actions</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">Backend & قواعد البيانات</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• Laravel Framework</li>
                    <li>• MySQL</li>
                    <li>• SQL Server</li>
                    <li>• RESTful APIs</li>
                    <li>• NewsData API</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">التصميم & UX/UI</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• Figma</li>
                    <li>• Responsive Design</li>
                    <li>• Dark/Light Mode</li>
                    <li>• RTL Support</li>
                    <li>• CSS Animations</li>
                    <li>• Component Design</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">المهارات الشخصية</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• سريع التعلم</li>
                    <li>• مهارة التواصل</li>
                    <li>• العمل ضمن فريق</li>
                    <li>• حل المشاكل</li>
                    <li>• إدارة المشاريع</li>
                    <li>• مواكبة التقنيات الحديثة</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-amber-500/20">
                <h4 className="font-semibold text-gray-200 mb-3 text-amber-300">مشروع الموقع الشخصي - التقنيات المستخدمة</h4>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <p className="text-gray-300 text-sm leading-relaxed">
                    تم بناء هذا الموقع باستخدام <span className="text-amber-400 font-semibold">React 18</span> مع
                    <span className="text-amber-400 font-semibold"> TypeScript</span> و
                    <span className="text-amber-400 font-semibold"> Vite</span> كأداة البناء،
                    <span className="text-amber-400 font-semibold"> Tailwind CSS</span> للتصميم المتجاوب،
                    <span className="text-amber-400 font-semibold"> React Query</span> لإدارة البيانات،
                    <span className="text-amber-400 font-semibold"> ShadCN/UI</span> للمكونات،
                    ونشر تلقائي على <span className="text-amber-400 font-semibold">GitHub Pages</span> باستخدام
                    <span className="text-amber-400 font-semibold"> GitHub Actions</span>.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default About;
